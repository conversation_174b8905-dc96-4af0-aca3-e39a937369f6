using UnityEngine;
using UnityEditor;

public class LogControlWindow : EditorWindow
{
    private LogManager logManager;
    private bool showLogs = true;
    private bool showWarnings = true;
    private bool showErrors = true;
    private bool enableLogging = true;

    private LogManager.LogMode currentMode = LogManager.LogMode.All;

    public static void ShowWindow()
    {
        GetWindow<LogControlWindow>("Log Control");
    }

    private void OnEnable()
    {
        // Try to find the LogManager in the scene, including inactive objects
        RefreshLogManager();

        // Sync UI with current LogManager state if found
        if (logManager != null)
        {
            SyncUIWithLogManager();
        }
    }

    private void OnGUI()
    {
        GUILayout.Label("Log Control Panel", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Status info
        if (logManager == null)
        {
            EditorGUILayout.HelpBox("LogManager not found in scene. Some features may be limited.", MessageType.Warning);

            if (GUILayout.Button("Refresh LogManager"))
            {
                RefreshLogManager();
            }
        }

        EditorGUILayout.Space();

        // Current status
        EditorGUILayout.LabelField("Current Status", EditorStyles.boldLabel);

        EditorGUI.BeginChangeCheck();

        enableLogging = EditorGUILayout.Toggle("Enable Logging", enableLogging);

        EditorGUI.BeginDisabledGroup(!enableLogging);

        showLogs = EditorGUILayout.Toggle("Show Logs", showLogs);
        showWarnings = EditorGUILayout.Toggle("Show Warnings", showWarnings);
        showErrors = EditorGUILayout.Toggle("Show Errors", showErrors);

        EditorGUI.EndDisabledGroup();

        EditorGUILayout.Space();

        // Quick presets
        GUILayout.Label("Quick Presets", EditorStyles.boldLabel);

        if (GUILayout.Button("Enable All Logs (Development)"))
        {
            SetLogSettings(true, true, true, true, LogManager.LogMode.All);
        }

        if (GUILayout.Button("Errors Only (Profiling)"))
        {
            SetLogSettings(true, false, false, true, LogManager.LogMode.ErrorsOnly);
        }

        if (GUILayout.Button("Warnings + Errors Only"))
        {
            SetLogSettings(true, false, true, true, LogManager.LogMode.WarningsAndErrors);
        }

        if (GUILayout.Button("Disable All Logs"))
        {
            SetLogSettings(false, false, false, false, LogManager.LogMode.None);
        }

        EditorGUILayout.Space();

        // Auto-apply when manual changes are made
        if (EditorGUI.EndChangeCheck())
        {
            ApplyLogSettings();
        }

        EditorGUILayout.Space();

        // Info box
        EditorGUILayout.HelpBox(
            "Use 'Errors Only' mode for profiling to reduce console spam while keeping important error messages.\n\n" +
            "Note: These settings only affect the Editor. Build settings are controlled separately.",
            MessageType.Info);
    }

    private void SetLogSettings(bool enable, bool logs, bool warnings, bool errors, LogManager.LogMode mode)
    {
        enableLogging = enable;
        showLogs = logs;
        showWarnings = warnings;
        showErrors = errors;
        currentMode = mode;

        // Immediately apply settings when using quick presets
        ApplyLogSettings();

        // Force UI refresh
        Repaint();
    }

    private void ApplyLogSettings()
    {
        // If we have a LogManager, use it
        if (logManager != null)
        {
            logManager.enableLogging = enableLogging;
            logManager.showLogs = showLogs;
            logManager.showWarnings = showWarnings;
            logManager.showErrors = showErrors;
            logManager.logMode = currentMode;
            logManager.ApplyLogSettings();

            Debug.Log($"Log settings applied via LogManager: {currentMode}");
        }
        else
        {
            // Fallback to direct control if no LogManager exists
            if (!enableLogging)
            {
                Debug.unityLogger.logEnabled = false;
                Debug.Log("Logging disabled");
            }
            else
            {
                Debug.unityLogger.logEnabled = true;

                // Set the filter based on what's enabled
                // Unity's filterLogType works differently - it sets the MINIMUM level to show
                LogType filter = LogType.Exception; // Start with most restrictive

                if (showLogs)
                {
                    filter = LogType.Log; // Show everything (Log, Warning, Error, Assert, Exception)
                }
                else if (showWarnings)
                {
                    filter = LogType.Warning; // Show warnings and above (Warning, Error, Assert, Exception)
                }
                else if (showErrors)
                {
                    filter = LogType.Error; // Show errors and above (Error, Assert, Exception)
                }

                Debug.unityLogger.filterLogType = filter;

                Debug.Log($"Log settings applied directly: Logs {(showLogs ? "enabled" : "disabled")}, " +
                         $"Warnings {(showWarnings ? "enabled" : "disabled")}, " +
                         $"Errors {(showErrors ? "enabled" : "disabled")}");
            }
        }
    }

    private void RefreshLogManager()
    {
        // Try multiple methods to find LogManager
        logManager = LogManager.Instance; // Try singleton first

        if (logManager == null)
        {
            // Try FindObjectOfType including inactive objects
            logManager = Object.FindObjectOfType<LogManager>(true);
        }

        if (logManager == null)
        {
            // Try finding in all loaded scenes
            for (int i = 0; i < UnityEngine.SceneManagement.SceneManager.sceneCount; i++)
            {
                var scene = UnityEngine.SceneManagement.SceneManager.GetSceneAt(i);
                if (scene.isLoaded)
                {
                    var rootObjects = scene.GetRootGameObjects();
                    foreach (var root in rootObjects)
                    {
                        logManager = root.GetComponentInChildren<LogManager>(true);
                        if (logManager != null) break;
                    }
                    if (logManager != null) break;
                }
            }
        }

        // Sync UI if LogManager found
        if (logManager != null)
        {
            SyncUIWithLogManager();
        }
    }

    private void SyncUIWithLogManager()
    {
        if (logManager != null)
        {
            enableLogging = logManager.enableLogging;
            showLogs = logManager.showLogs;
            showWarnings = logManager.showWarnings;
            showErrors = logManager.showErrors;
            currentMode = logManager.logMode;
        }
    }

    private void OnInspectorUpdate()
    {
        // Refresh the window occasionally and check for LogManager if missing
        if (logManager == null)
        {
            RefreshLogManager();
        }
        Repaint();
    }
}