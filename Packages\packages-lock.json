{"dependencies": {"com.arongranberg.astar": {"version": "5.4.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.7", "com.unity.collections": "1.5.1", "com.unity.mathematics": "1.2.6", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.ugui": "1.0.0"}, "url": "https://arongranberg.com/packages/81c7e795f8545d505aebdbb793fd55456283d908871e1"}, "com.autodesk.fbx": {"version": "5.1.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.cysharp.csprojmodifier": {"version": "https://github.com/Cysharp/CsprojModifier.git?path=src/CsprojModifier/Assets/CsprojModifier", "depth": 0, "source": "git", "dependencies": {}, "hash": "e82080dac67f22fa0ef0b7c0aceadda92df76ec3"}, "com.cysharp.unitask": {"version": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "depth": 0, "source": "git", "dependencies": {}, "hash": "8042b29ff87dd5506d7aad72bd6d8d7405985f27"}, "com.github-glitchenzo.nugetforunity": {"version": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "depth": 0, "source": "git", "dependencies": {}, "hash": "a22299e7e94eaa13ac17b326e0de8f2d3afa9e46"}, "com.justinpbarnett.unity-mcp": {"version": "https://github.com/justinpbarnett/unity-mcp.git?path=/UnityMcpBridge", "depth": 0, "source": "git", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.0.2"}, "hash": "cbe9b3a2f00e1bbc8f353ea71514580732922ed7"}, "com.kyrylokuzyk.primetween": {"version": "file:../Assets/Plugins/PrimeTween/internal/com.kyrylokuzyk.primetween.tgz", "depth": 0, "source": "local-tarball", "dependencies": {}}, "com.matthewminer.position-visualizer": {"version": "https://github.com/mminer/position-visualizer.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "a22376fc715581bedc0eb3dded2c0bca7b2aa8f7"}, "com.occasoftware.altos": {"version": "file:com.occasoftware.altos", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7", "com.unity.visualeffectgraph": "14.0.7"}}, "com.occasoftware.auto-exposure": {"version": "file:com.occasoftware.auto-exposure", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.bloom": {"version": "file:com.occasoftware.bloom", "depth": 0, "source": "embedded", "dependencies": {}}, "com.occasoftware.buto": {"version": "file:com.occasoftware.buto", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.haze-fx": {"version": "file:com.occasoftware.haze-fx", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "12.1.6"}}, "com.occasoftware.lspp": {"version": "file:com.occasoftware.lspp", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.outline-objects": {"version": "file:com.occasoftware.outline-objects", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.radial-blur": {"version": "file:com.occasoftware.radial-blur", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.toon-kit-2": {"version": "file:com.occasoftware.toon-kit-2", "depth": 0, "source": "embedded", "dependencies": {"com.unity.shadergraph": "14.0.7"}}, "com.occasoftware.ultimate-lit-shader": {"version": "file:com.occasoftware.ultimate-lit-shader", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.universal": "14.0.7"}}, "com.occasoftware.vfx-library": {"version": "file:com.occasoftware.vfx-library", "depth": 0, "source": "embedded", "dependencies": {}}, "com.projectdawn.impostor": {"version": "file:com.projectdawn.impostor", "depth": 0, "source": "embedded", "dependencies": {"com.unity.shadergraph": "12.1.10", "com.unity.render-pipelines.core": "12.1.10", "com.unity.mathematics": "1.2.1"}}, "com.tnd.upscaler.fsr3": {"version": "file:com.tnd.upscaler.fsr3", "depth": 0, "source": "embedded", "dependencies": {"com.tnd.upscaling": "1.0.0"}}, "com.tnd.upscaling": {"version": "file:com.tnd.upscaling", "depth": 0, "source": "embedded", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.2d.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.tilemap": "1.0.0", "com.unity.modules.uielements": "1.0.0"}}, "com.unity.analytics": {"version": "3.8.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.analytics": "1.0.4"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.24", "depth": 0, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.cinemachine": {"version": "3.1.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.splines": "2.0.0", "com.unity.modules.imgui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.8.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.6", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.entities": {"version": "1.3.14", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.collections": "2.5.7", "com.unity.mathematics": "1.3.2", "com.unity.modules.audio": "1.0.0", "com.unity.serialization": "3.1.2", "com.unity.profiling.core": "1.0.2", "com.unity.modules.physics": "1.0.0", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "1.21.25", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.formats.fbx": {"version": "5.1.3", "depth": 0, "source": "registry", "dependencies": {"com.autodesk.fbx": "5.1.1", "com.unity.timeline": "1.7.1"}, "url": "https://packages.unity.com"}, "com.unity.ide.cursor": {"version": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "depth": 0, "source": "git", "dependencies": {"com.unity.test-framework": "1.1.9"}, "hash": "2c0153a9bab1abc783e5599fa2d2cc3c69d5e389"}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.inputsystem": {"version": "1.14.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.memoryprofiler": {"version": "1.1.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.0", "com.unity.collections": "1.2.3", "com.unity.mathematics": "1.2.1", "com.unity.profiling.core": "1.0.0", "com.unity.editorcoroutines": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.center": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.uielements": "1.0.0"}}, "com.unity.nuget.mono-cecil": {"version": "1.11.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.performance.profile-analyzer": {"version": "1.2.3", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.postprocessing": {"version": "3.5.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.physics": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.project-auditor": {"version": "1.0.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.nuget.mono-cecil": "1.10.1", "com.unity.nuget.newtonsoft-json": "3.2.1"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "17.2.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0", "com.unity.shadergraph": "17.2.0", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "file:com.unity.render-pipelines.universal-config", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.core": "14.0.9"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "2.4.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.3", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.serialization": {"version": "3.1.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.7.2", "com.unity.collections": "2.4.2"}, "url": "https://packages.unity.com"}, "com.unity.services.analytics": {"version": "6.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.12.4", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.services.cloud-build": {"version": "2.0.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.collab-proxy": "2.6.0", "com.unity.services.core": "1.7.0", "com.unity.nuget.newtonsoft-json": "3.0.2"}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.14.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.1.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0", "com.unity.searcher": "4.9.3"}}, "com.unity.splines": {"version": "2.8.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.imgui": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.5.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.8.9", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.visualeffectgraph": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.shadergraph": "17.2.0", "com.unity.render-pipelines.core": "17.2.0"}}, "jp.keijiro.duotone": {"version": "2.2.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://registry.npmjs.com"}, "jp.keijiro.kino.post-processing.eight.universal": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.render-pipelines.universal": "17.0.0"}, "url": "https://registry.npmjs.com"}, "jp.keijiro.klak.ui-toolkit-assets": {"version": "0.2.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://registry.npmjs.com"}, "org.happy-turtle.order-independent-transparency": {"version": "file:org.happy-turtle.order-independent-transparency", "depth": 0, "source": "embedded", "dependencies": {}}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.nvidia": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0", "com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}