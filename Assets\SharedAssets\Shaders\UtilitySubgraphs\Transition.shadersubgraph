{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "51deffc34db348a4bcacbc640d6348b0",
    "m_Properties": [
        {
            "m_Id": "TransitionSample"
        },
        {
            "m_Id": "EdgeFadeTime"
        },
        {
            "m_Id": "t"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "29480d89000d447682b49c340b839c52"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "Input_TransitionSample"
        },
        {
            "m_Id": "Input_EdgeFadeTime"
        },
        {
            "m_Id": "Input_t"
        },
        {
            "m_Id": "Subtract"
        },
        {
            "m_Id": "Absolute"
        },
        {
            "m_Id": "Smoothstep"
        },
        {
            "m_Id": "OneMinus"
        },
        {
            "m_Id": "Output_Fade"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Input_TransitionSample"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Subtract"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Input_t"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Subtract"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Subtract"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Absolute"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Absolute"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Smoothstep"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Input_EdgeFadeTime"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Smoothstep"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "Smoothstep"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "OneMinus"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "OneMinus"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "Output_Fade"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 0,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "Output_Fade"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "TransitionSample",
    "m_Id": 0,
    "m_DisplayName": "TransitionSample",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "TransitionSample",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "EdgeFadeTime",
    "m_Id": 0,
    "m_DisplayName": "EdgeFadeTime",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "EdgeFadeTime",
    "m_StageCapability": 3,
    "m_Value": 0.1,
    "m_DefaultValue": 0.1,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "t",
    "m_Id": 0,
    "m_DisplayName": "t",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "t",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphInputNode",
    "m_ObjectId": "Input_TransitionSample",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sub Graph Input",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -400.0,
            "y": -100.0,
            "width": 150.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "TransitionSample"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "TransitionSample"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphInputNode",
    "m_ObjectId": "Input_EdgeFadeTime",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sub Graph Input",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -400.0,
            "y": 0.0,
            "width": 150.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "EdgeFadeTime"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "EdgeFadeTime"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphInputNode",
    "m_ObjectId": "Input_t",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sub Graph Input",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -400.0,
            "y": 100.0,
            "width": 150.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "t"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "t"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "Subtract",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -200.0,
            "y": -50.0,
            "width": 130.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "Subtract_A"
        },
        {
            "m_Id": "Subtract_B"
        },
        {
            "m_Id": "Subtract_Out"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Subtract_A",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Subtract_B",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Subtract_Out",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "Absolute",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": -50.0,
            "width": 132.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "Absolute_In"
        },
        {
            "m_Id": "Absolute_Out"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Absolute_In",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Absolute_Out",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SmoothstepNode",
    "m_ObjectId": "Smoothstep",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Smoothstep",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 200.0,
            "y": -50.0,
            "width": 150.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "Smoothstep_In"
        },
        {
            "m_Id": "Smoothstep_Edge0"
        },
        {
            "m_Id": "Smoothstep_Edge1"
        },
        {
            "m_Id": "Smoothstep_Out"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Smoothstep_In",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Smoothstep_Edge0",
    "m_Id": 1,
    "m_DisplayName": "Edge0",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Edge0",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Smoothstep_Edge1",
    "m_Id": 2,
    "m_DisplayName": "Edge1",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Edge1",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Smoothstep_Out",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "OneMinus",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 400.0,
            "y": -50.0,
            "width": 128.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "OneMinus_In"
        },
        {
            "m_Id": "OneMinus_Out"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "OneMinus_In",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "OneMinus_Out",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "Output_Fade",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sub Graph Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 600.0,
            "y": -50.0,
            "width": 150.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "Fade"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "Fade",
    "m_Id": 0,
    "m_DisplayName": "Fade",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Fade",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}