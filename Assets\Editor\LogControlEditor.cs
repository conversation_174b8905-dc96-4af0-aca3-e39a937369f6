using UnityEngine;
using UnityEditor;

public class LogControlEditor : Editor
{
    [MenuItem("BTR/Log Control")]
    public static void OpenLogControlWindow()
    {
        LogControlWindow.ShowWindow();
    }

    private static void SetLogSettings(bool enable, bool logs, bool warnings, bool errors)
    {
        // Try to find and update the LogManager if it exists
        LogManager logManager = Object.FindObjectOfType<LogManager>();
        if (logManager != null)
        {
            logManager.enableLogging = enable;
            logManager.showLogs = logs;
            logManager.showWarnings = warnings;
            logManager.showErrors = errors;

            // Determine the mode based on settings
            if (!enable)
            {
                logManager.logMode = LogManager.LogMode.None;
            }
            else if (logs && warnings && errors)
            {
                logManager.logMode = LogManager.LogMode.All;
            }
            else if (!logs && !warnings && errors)
            {
                logManager.logMode = LogManager.LogMode.ErrorsOnly;
            }
            else if (!logs && warnings && errors)
            {
                logManager.logMode = LogManager.LogMode.WarningsAndErrors;
            }

            logManager.ApplyLogSettings();

            Debug.Log($"Log settings updated via LogManager: Enable={enable}, Logs={logs}, Warnings={warnings}, Errors={errors}");
        }
        else
        {
            // Fallback to direct control if no LogManager exists
            if (!enable)
            {
                Debug.unityLogger.logEnabled = false;
                Debug.Log("Logging disabled");
            }
            else
            {
                Debug.unityLogger.logEnabled = true;

                // Set the filter based on what's enabled
                LogType filter = LogType.Error; // Always include errors if logging is enabled

                if (warnings)
                    filter |= LogType.Warning;

                if (logs)
                    filter |= LogType.Log;

                Debug.unityLogger.filterLogType = filter;

                Debug.Log($"Log settings applied directly: Logs {(logs ? "enabled" : "disabled")}, " +
                         $"Warnings {(warnings ? "enabled" : "disabled")}, " +
                         $"Errors {(errors ? "enabled" : "disabled")}");
            }
        }
    }
}